{"user_id": "1679928246690009088", "interactions_count": 12, "interactions": [{"conversation_id": "1753433500789702739", "tweet_id": "1753435728455823531", "timestamp": "2024-02-02T15:10:29+00:00", "timestamp_unix": 1706886629, "type": "comment", "text": "@PeterSchiff <PERSON>hi<PERSON><PERSON> is schiffting the narrative. Clown.", "context": {"type": "tweet", "id": "1753681195211305006", "text": "@PeterSchiff ...I think intelligence/smarts is reflected by learning from your mistakes and being somewhat flexible in your positions. This is going to get ugly. $BTC $ETH", "author_id": "1317065369912176640", "author_username": "stonkmesideways"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 50}}, {"conversation_id": "1757523687807263032", "tweet_id": "1757536230328123609", "timestamp": "2024-02-13T22:44:25+00:00", "timestamp_unix": 1707864265, "type": "comment", "text": "@PeterSchiff Is <PERSON><PERSON><PERSON> sounding like a conman or a clown?", "context": {"type": "tweet", "id": "1758166765417459998", "text": "And the reality is the inflation we feel day-to-day well exceeds the headline 3.1% figure we saw earlier this week. Real interest rates are becoming more and more negative which will ultimately drive #gold higher.", "author_id": "1748850388659474432", "author_username": "DG_Garofalo"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 110}}, {"conversation_id": "1757523687807263032", "tweet_id": "1757628250757423158", "timestamp": "2024-02-14T04:50:04+00:00", "timestamp_unix": 1707886204, "type": "comment", "text": "@PeterSchiff Schiffs gold fund dropped 4.2% today.", "context": {"type": "tweet", "id": "1758166765417459998", "text": "And the reality is the inflation we feel day-to-day well exceeds the headline 3.1% figure we saw earlier this week. Real interest rates are becoming more and more negative which will ultimately drive #gold higher.", "author_id": "1748850388659474432", "author_username": "DG_Garofalo"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 25}}, {"conversation_id": "1767542431975677965", "tweet_id": "1767546108517974224", "timestamp": "2024-03-12T13:40:06+00:00", "timestamp_unix": 1710250806, "type": "comment", "text": "@PeterSchiff Last week they got it now they dont. Schiffs pie in the sky week is over. Put him back in the barn . Humpty dumpty is back.", "context": {"type": "tweet", "id": "1767848173337509892", "text": "https://t.co/CRCCWZurYL https://t.co/FPgjqwUEvo", "author_id": "1553941685792829441", "author_username": "neh<PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1770530776783700139", "tweet_id": "1770681140594033008", "timestamp": "2024-03-21T05:17:36+00:00", "timestamp_unix": 1710998256, "type": "comment", "text": "@PeterSchiff No pivot. B.S. talk.", "context": {"type": "tweet", "id": "1770847533960401131", "text": "Silver just gave back all of its gains and then some.", "author_id": "1760690447402389504", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1793289721038221662", "tweet_id": "1793448322667020712", "timestamp": "2024-05-23T01:06:15+00:00", "timestamp_unix": 1716426375, "type": "comment", "text": "@PeterSchiff Today they dont get it. Got it.", "context": {"type": "tweet", "id": "1793383457814393217", "text": "#RATE #CUTS", "author_id": "2544660388", "author_username": "Silver__Santa"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1801700962245263659", "tweet_id": "1801886041025970393", "timestamp": "2024-06-15T07:54:44+00:00", "timestamp_unix": 1718438084, "type": "comment", "text": "@PeterSchiff Thats your story.", "context": {"type": "tweet", "id": "1805926932774519232", "text": "Wreckless spending and endless wars 🔥", "author_id": "1628460661310201858", "author_username": "WhoBee5"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 18}}, {"conversation_id": "1819411043061203048", "tweet_id": "1819418198045085783", "timestamp": "2024-08-02T17:01:16+00:00", "timestamp_unix": 1722618076, "type": "comment", "text": "@PeterSchiff Your B.S. meter is heating up .", "context": {"type": "tweet", "id": "1820098711843733840", "text": "Big if tru.", "author_id": "354462175", "author_username": "DerAchsenZeit"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1833251464891748799", "tweet_id": "1833282226827452477", "timestamp": "2024-09-09T23:11:58+00:00", "timestamp_unix": 1725923518, "type": "comment", "text": "@PeterSchiff Fear.", "context": {"type": "tweet", "id": "1833903400230211953", "text": "@DougKass Game over @DougKass", "author_id": "190808219", "author_username": "geomadegeo"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 48}}, {"conversation_id": "1854223116656156777", "tweet_id": "1854319368580030737", "timestamp": "2024-11-07T00:26:03+00:00", "timestamp_unix": 1730939163, "type": "comment", "text": "@PeterSchiff The oracle has spoken.", "context": {"type": "tweet", "id": "1854511387180941665", "text": "#uranium", "author_id": "1735102238920441856", "author_username": "tailor488058"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 41}}, {"conversation_id": "1854165373559407010", "tweet_id": "1854320195994529926", "timestamp": "2024-11-07T00:29:20+00:00", "timestamp_unix": 1730939360, "type": "comment", "text": "@PeterSchiff The drunk vaccinated oracle has spoken .", "context": {"type": "tweet", "id": "1854789774193655952", "text": "@fujimaki_takesi 私にとって、ピーター・シフの分析は、起こりそうなことに最も近いものです。https://t.co/NVv19fL6Pb", "author_id": "1241780365364166656", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1869463931359748587", "tweet_id": "1869473033032999241", "timestamp": "2024-12-18T20:01:18+00:00", "timestamp_unix": 1734552078, "type": "comment", "text": "@PeterSchiff @<PERSON><PERSON><PERSON><PERSON> just gave <PERSON><PERSON><PERSON> more time to spread the B.S.", "context": {"type": "tweet", "id": "1869492167372386582", "text": "💯", "author_id": "1277683063259115522", "author_username": "desmarais_manon"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 49}}]}