{"user_id": "1794759172333981696", "interactions_count": 13, "interactions": [{"conversation_id": "1836466033819029984", "tweet_id": "1836468843117748404", "timestamp": "2024-09-18T18:14:26+00:00", "timestamp_unix": 1726683266, "type": "comment", "text": "@byHeatherLong Contrarian view - very questionable whether this is \"good news\" holistically - on multiple fronts - will depend on overall response.  \n\nPrimary on the concern front is that sticky inflation may become \"stickier\".", "context": {"type": "tweet", "id": "1840819295452004508", "text": "2. The Fed Cuts Rates 50 bps\nMortgage rates are projected to be 6.2% by end of year -- where they are now. Wait, what?\n\nImpact on SMB: ✅\n- Bump in Consumer spending\n- Lower borrowing costs\n- Potential bump in demand for RE dependent businesses\n\nhttps://t.co/5SBrO5YVTC", "author_id": "1640876890616373248", "author_username": "CoFoundersNik"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 911}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836470254102368619", "timestamp": "2024-09-18T18:20:03+00:00", "timestamp_unix": 1726683603, "type": "quote", "text": "Contrarian view = questionable whether this is \"good news\" holistically - on multiple fronts.  Degree of \"good\" or \"not good\" will depend on overall response(s).\n\nOne primary concern: sticky inflation may become \"stickier\".", "context": {"type": "tweet", "id": "1836466033819029984", "text": "JUST IN: Federal Reserve cuts interest rates 50 bps, the first rate cut since 2020.\nThe new interest rate target is 4.75 to 5%.\n\n—&gt;The “dot plot” signals interest rate down to 4.25 to 4.5% by the end of the year (2 add'l cuts likely)\n\nVote today was 11 in favor and 1 against https://t.co/qmRFhcZoAX", "author_id": "257354839", "author_username": "byHeatherLong"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 41}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836510329901322584", "timestamp": "2024-09-18T20:59:18+00:00", "timestamp_unix": 1726693158, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong Firstly, the metric you chose is not the appropriate one. You regurgitated the CPI core core YOY metric. The Fed targets the PCE rate – Personal Consumption Expenditures (the attached FRED graph does not include August’s data – but still historically above target) https://t.co/AEafTyod06", "context": {"type": "tweet", "id": "1836479291665559762", "text": "@Morgan_Ag_Cnslt @byHeatherLong Core PCE is like 2.4% - no one is worried if it gets “stuck” there - that is where they want it. Inflations IS NOT HIGH ANYMORE, hence the begin of rate cuts with more to follow.", "author_id": "1382807189912436736", "author_username": "jeffrey56222295"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 25}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836510424788869271", "timestamp": "2024-09-18T20:59:40+00:00", "timestamp_unix": 1726693180, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong That current PCE rate is 262% UP from 2.58% last month (i.e. trending UPWARD). This rate is 31% GREATER than the targeted 2.0% PCE rate. Would your company (or any) be satisfied with an actual to target error of 31%? Few would.", "context": {"type": "tweet", "id": "1836510329901322584", "text": "@jeffrey56222295 @byHeatherLong Firstly, the metric you chose is not the appropriate one. You regurgitated the CPI core core YOY metric. The Fed targets the PCE rate – Personal Consumption Expenditures (the attached FRED graph does not include August’s data – but still historically above target) https://t.co/AEafTyod06", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836510529747325220", "timestamp": "2024-09-18T21:00:05+00:00", "timestamp_unix": 1726693205, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong Secondly, your metric is a simple static analysis – inflationary pressures are significantly more complex and dynamic.", "context": {"type": "tweet", "id": "1836510424788869271", "text": "@jeffrey56222295 @byHeatherLong That current PCE rate is 262% UP from 2.58% last month (i.e. trending UPWARD). This rate is 31% GREATER than the targeted 2.0% PCE rate. Would your company (or any) be satisfied with an actual to target error of 31%? Few would.", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836510805430600107", "timestamp": "2024-09-18T21:01:11+00:00", "timestamp_unix": 1726693271, "type": "comment", "text": "The M2 Money Supply remains historically high – increased wages (and they have increased in aggregate) - without a corresponding increase in productivity - chasing after the same bundle of goods results in upward inflationary pressure. Additionally, the increase in the velocity of money (how fast $ “churns” in an economy) increases inflationary pressure.", "context": {"type": "tweet", "id": "1836510529747325220", "text": "@jeffrey56222295 @byHeatherLong Secondly, your metric is a simple static analysis – inflationary pressures are significantly more complex and dynamic.", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 22}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836510896476295508", "timestamp": "2024-09-18T21:01:33+00:00", "timestamp_unix": 1726693293, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong https://t.co/otmWceTWEl", "context": {"type": "tweet", "id": "1836510805430600107", "text": "The M2 Money Supply remains historically high – increased wages (and they have increased in aggregate) - without a corresponding increase in productivity - chasing after the same bundle of goods results in upward inflationary pressure. Additionally, the increase in the velocity of money (how fast $ “churns” in an economy) increases inflationary pressure.", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836511142757437751", "timestamp": "2024-09-18T21:02:31+00:00", "timestamp_unix": 1726693351, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong https://t.co/e0lwpfoUvD", "context": {"type": "tweet", "id": "1836510896476295508", "text": "@jeffrey56222295 @byHeatherLong https://t.co/otmWceTWEl", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836511393182335478", "timestamp": "2024-09-18T21:03:31+00:00", "timestamp_unix": 1726693411, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong https://t.co/6NKbr5O8oF", "context": {"type": "tweet", "id": "1836510896476295508", "text": "@jeffrey56222295 @byHeatherLong https://t.co/otmWceTWEl", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836511492319121911", "timestamp": "2024-09-18T21:03:55+00:00", "timestamp_unix": 1726693435, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong https://t.co/69Jzq9b64j", "context": {"type": "tweet", "id": "1836511393182335478", "text": "@jeffrey56222295 @byHeatherLong https://t.co/6NKbr5O8oF", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 3}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836512077852311681", "timestamp": "2024-09-18T21:06:14+00:00", "timestamp_unix": 1726693574, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong Thee are basic macroeconomic principles.\n\nI throw this in for good measure – inflation (even WITHOUT considering food and energy – who doesn’t eat and drive) REMAINS a problem for the typical consumer. https://t.co/jLNpddfVLy", "context": {"type": "tweet", "id": "1836511393182335478", "text": "@jeffrey56222295 @byHeatherLong https://t.co/6NKbr5O8oF", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836512432078147607", "timestamp": "2024-09-18T21:07:39+00:00", "timestamp_unix": 1726693659, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong 2.62% I apologize for the typo.", "context": {"type": "tweet", "id": "1836510424788869271", "text": "@jeffrey56222295 @byHeatherLong That current PCE rate is 262% UP from 2.58% last month (i.e. trending UPWARD). This rate is 31% GREATER than the targeted 2.0% PCE rate. Would your company (or any) be satisfied with an actual to target error of 31%? Few would.", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 3}}, {"conversation_id": "1836466033819029984", "tweet_id": "1836512994559193123", "timestamp": "2024-09-18T21:09:53+00:00", "timestamp_unix": 1726693793, "type": "comment", "text": "@jeffrey56222295 @byHeatherLong I therefore stand by my original comment.", "context": {"type": "tweet", "id": "1836512077852311681", "text": "@jeffrey56222295 @byHeatherLong Thee are basic macroeconomic principles.\n\nI throw this in for good measure – inflation (even WITHOUT considering food and energy – who doesn’t eat and drive) REMAINS a problem for the typical consumer. https://t.co/jLNpddfVLy", "author_id": "1794759172333981696", "author_username": "Morgan_Ag_Cnslt"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}]}