#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
interests数据处理脚本
使用MACIT框架处理interests_intera_data目录中的数据，并输出到interests_output文件夹
"""

import argparse
import json
import logging
import os
import sys
import time
from pathlib import Path

from logger_config import setup_logger
from macit_framework import MACITFramework
from data_utils import get_all_user_ids

# 设置日志
logger = setup_logger('interests_processor')


def parse_args():
    """
    解析命令行参数

    Returns:
        解析后的参数
    """
    parser = argparse.ArgumentParser(description='interests数据处理 - 使用MACIT框架处理interests数据')

    parser.add_argument(
        '--user_id',
        type=str,
        default=None,
        help='要分析的用户ID，如果不指定则分析所有用户',
    )

    parser.add_argument(
        '--interaction_index',
        type=int,
        default=0,
        help='要分析的交互索引，默认为0',
    )

    parser.add_argument(
        '--max_rounds',
        type=int,
        default=None,
        help='最大辩论轮数，如果不指定则使用配置文件中的值',
    )

    parser.add_argument(
        '--max_users',
        type=int,
        default=None,
        help='最大分析用户数量，如果不指定则分析所有用户',
    )

    parser.add_argument(
        '--start_from',
        type=int,
        default=0,
        help='从第几个用户开始处理（用于断点续传）',
    )

    return parser.parse_args()


def get_interests_user_ids():
    """
    获取interests数据目录中的所有用户ID

    Returns:
        用户ID列表
    """
    interests_data_dir = Path("data/cut/interests_intera_data/intera_data")

    if not interests_data_dir.exists():
        logger.error(f"interests数据目录不存在: {interests_data_dir}")
        return []

    # 获取所有JSON文件
    json_files = list(interests_data_dir.glob("*.json"))
    user_ids = [f.stem for f in json_files]

    logger.info(f"在interests数据目录中发现 {len(user_ids)} 个用户数据文件")
    return sorted(user_ids)


def save_interests_result(result, user_id, interaction_index=0):
    """
    保存interests分析结果到interests_output文件夹

    Args:
        result: 分析结果
        user_id: 用户ID
        interaction_index: 交互索引

    Returns:
        保存的文件路径
    """
    # 创建interests输出目录
    interests_output_dir = Path("interests_output")
    interests_output_dir.mkdir(exist_ok=True)

    # 创建用户专属目录
    user_output_dir = interests_output_dir / user_id
    user_output_dir.mkdir(exist_ok=True)

    # 保存结果文件
    output_file = user_output_dir / f"interaction_{interaction_index}.json"

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    logger.info(f"interests分析结果已保存到: {output_file}")
    return output_file



def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 创建MACIT框架
    logger.info("正在初始化MACIT框架用于interests数据处理...")
    framework = MACITFramework()
    logger.info("MACIT框架初始化完成，包含4个智能体")

    # 确定要分析的用户ID列表
    if args.user_id:
        user_ids = [args.user_id]
    else:
        # 获取interests数据目录中的所有用户ID
        user_ids = get_interests_user_ids()

        if not user_ids:
            logger.error("未找到任何interests用户数据文件")
            return

        logger.info(f"发现 {len(user_ids)} 个interests用户数据文件")

        # 应用起始位置
        if args.start_from > 0:
            user_ids = user_ids[args.start_from:]
            logger.info(f"从第 {args.start_from + 1} 个用户开始处理")

        # 应用最大用户数限制
        if args.max_users:
            user_ids = user_ids[:args.max_users]
            logger.info(f"限制处理前 {args.max_users} 个用户")

    logger.info(f"将要分析 {len(user_ids)} 个interests用户的细粒度结构化意图")

    # 分析每个用户的意图
    successful_analyses = 0
    failed_analyses = 0
    skipped_analyses = 0

    for i, user_id in enumerate(user_ids, 1):
        logger.info(f"正在分析interests用户 {user_id} ({i}/{len(user_ids)})...")

        try:
            # 检查是否已经处理过
            interests_output_dir = Path("interests_output")
            user_output_dir = interests_output_dir / user_id
            output_file = user_output_dir / f"interaction_{args.interaction_index}.json"

            if output_file.exists():
                logger.info(f"用户 {user_id} 已经处理过，跳过")
                skipped_analyses += 1
                continue

            # 分析用户意图 - 使用interests数据
            result = framework.analyze_user_intent(
                user_id=user_id,
                interaction_index=args.interaction_index,
                max_rounds=args.max_rounds,
                use_interests_data=True,
            )

            # 保存分析结果到interests_output
            output_path = save_interests_result(
                result=result,
                user_id=user_id,
                interaction_index=args.interaction_index,
            )

            # 打印简要结果
            print(f"\n{'='*50}")
            print(f"interests用户 {user_id} 分析完成:")
            print(f"{'='*50}")

            # 最终结构化意图标签
            final_label = result.get('final_structured_intent_label', {})
            if final_label:
                print(f"🎯 结构化意图标签:")
                print(f"  - 事件背景: {final_label.get('event_background', 'N/A')}")
                print(f"  - 具体话题: {final_label.get('specific_topic', 'N/A')}")
                print(f"  - 意图分类: {final_label.get('coarse_intent_category', 'N/A')}")
                print(f"  - 用户立场: {final_label.get('user_stance', 'N/A')}")
                print(f"  - 置信度: {final_label.get('confidence_score', 'N/A')}")

            print(f"💾 结果已保存到: {output_path}")

            logger.info(f"interests用户 {user_id} 的细粒度意图分析完成")
            successful_analyses += 1

        except ValueError as e:
            # 内容安全检查失败，跳过该用户
            logger.warning(f"跳过interests用户 {user_id}: {e}")
            failed_analyses += 1

        except Exception as e:
            # 检查是否是DeepSeek内容风险错误
            error_msg = str(e)
            if "Content Exists Risk" in error_msg or "Content Risk" in error_msg:
                logger.warning(f"interests用户 {user_id} 触发内容风险检查，跳过")
                failed_analyses += 1
            else:
                logger.error(f"分析interests用户 {user_id} 时出错: {e}")
                import traceback
                traceback.print_exc()
                failed_analyses += 1

        # 添加延迟，避免过快请求
        time.sleep(2)

    # 输出最终统计
    logger.info("所有interests用户的细粒度意图分析已完成")
    logger.info(f"统计结果: 成功分析 {successful_analyses} 个用户，失败 {failed_analyses} 个用户，跳过 {skipped_analyses} 个用户")
    total_processed = successful_analyses + failed_analyses
    if total_processed > 0:
        logger.info(f"成功率: {successful_analyses/total_processed*100:.1f}%")


if __name__ == "__main__":
    main()
