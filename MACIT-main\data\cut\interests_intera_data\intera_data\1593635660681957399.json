{"user_id": "1593635660681957399", "interactions_count": 14, "interactions": [{"conversation_id": "1836467290449006999", "tweet_id": "1836468859575861438", "timestamp": "2024-09-18T18:14:30+00:00", "timestamp_unix": 1726683270, "type": "comment", "text": "@RpsAgainstTrump Fantastic news! Bravo, Fed!", "context": {"type": "tweet", "id": "1836491390861169033", "text": "See it didn’t fall.", "author_id": "950741637512286208", "author_username": "Jyork2018"}, "metrics": {"retweet_count": 2, "reply_count": 3, "like_count": 53, "quote_count": 1, "view_count": 1590}}, {"conversation_id": "1836468450874548638", "tweet_id": "1836469453493596596", "timestamp": "2024-09-18T18:16:52+00:00", "timestamp_unix": 1726683412, "type": "comment", "text": "@Crochet4Lyfe <PERSON> is gonna be bigggggg mad.", "context": {"type": "tweet", "id": "1836485253759705127", "text": "Let’s go!!!!", "author_id": "1796321241789313024", "author_username": "BiatchDulce"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 184}}, {"conversation_id": "1836468450874548638", "tweet_id": "1836470280757137852", "timestamp": "2024-09-18T18:20:09+00:00", "timestamp_unix": 1726683609, "type": "comment", "text": "@peters_kasimir Why do you pretend like you know what you're talking about?\n\nMore money circulating in the economy doesn't mean more money is being printed, ma'am.", "context": {"type": "tweet", "id": "1836469225084309744", "text": "@ArtCandee Why are you so happy about more money printing? https://t.co/5aBKClrtJt", "author_id": "2504955389", "author_username": "life_oftree"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 2, "quote_count": 0, "view_count": 112}}, {"conversation_id": "1836468450874548638", "tweet_id": "1836470334595436985", "timestamp": "2024-09-18T18:20:22+00:00", "timestamp_unix": 1726683622, "type": "comment", "text": "@FourYearsBetter We've gotta show up!", "context": {"type": "tweet", "id": "1836469470602412533", "text": "@ArtCandee Time to VOTE 💙🇺🇸", "author_id": "1769105024007725056", "author_username": "FourYearsBetter"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 123}}, {"conversation_id": "1836466752752095371", "tweet_id": "1836470452946075910", "timestamp": "2024-09-18T18:20:50+00:00", "timestamp_unix": 1726683650, "type": "comment", "text": "@MeidasTouch This is fantastic news!", "context": {"type": "tweet", "id": "1836584292711043431", "text": "#American people", "author_id": "58915476", "author_username": "Ninavictoria_10"}, "metrics": {"retweet_count": 3, "reply_count": 1, "like_count": 77, "quote_count": 0, "view_count": 2685}}, {"conversation_id": "1836468450874548638", "tweet_id": "1836470972972429316", "timestamp": "2024-09-18T18:22:54+00:00", "timestamp_unix": 1726683774, "type": "comment", "text": "@BuffaloJon1 Cheaper car, house, and big ticket loan payments.\n\nLower credit card interest rates. \n\nMore money to be spent elsewhere!", "context": {"type": "tweet", "id": "1836470371618267634", "text": "@ArtCandee This may not be felt before the Nov 5th, but this is HUGE news for this \"vibes election\" \n\nHere is hoping <PERSON> talks about how this rate cut will help the middle class and how she and <PERSON><PERSON> are responsible for it.", "author_id": "1484876418710392835", "author_username": "BuffaloJon1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 155}}, {"conversation_id": "1836468450874548638", "tweet_id": "1836471094070682056", "timestamp": "2024-09-18T18:23:23+00:00", "timestamp_unix": 1726683803, "type": "comment", "text": "@FreeToRoar87 I literally speak in Spaces all the time. <PERSON> and seethe, weirdo.", "context": {"type": "tweet", "id": "1836470356640698436", "text": "@ArtCandee ArtCandee has 99,800 posts since November of 2022. That’s 4,536 posts a month or 151 posts a day. This is not a person. This is part of the propaganda machine 🕵️", "author_id": "1455294510078955520", "author_username": "FreeToRoar87"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 79}}, {"conversation_id": "1836468450874548638", "tweet_id": "1836473293559267658", "timestamp": "2024-09-18T18:32:07+00:00", "timestamp_unix": 1726684327, "type": "comment", "text": "@alexborlowski It's great news!", "context": {"type": "tweet", "id": "1836471522166259750", "text": "@ArtCandee As a mortgage professional, this is music to my ears🙏🏻", "author_id": "1430244907030036490", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 127}}, {"conversation_id": "1836468450874548638", "tweet_id": "1836473347506425905", "timestamp": "2024-09-18T18:32:20+00:00", "timestamp_unix": 1726684340, "type": "comment", "text": "@GlassbutterflyB Music to homebuyers' ears too!", "context": {"type": "tweet", "id": "1836470756982624546", "text": "@ArtCandee As a mortgage lender...this makes me HAPPY", "author_id": "1744425791176192000", "author_username": "GlassbutterflyB"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 93}}, {"conversation_id": "1836468450874548638", "tweet_id": "1836497027192230354", "timestamp": "2024-09-18T20:06:26+00:00", "timestamp_unix": 1726689986, "type": "tweet", "text": "@Putin0wnsTrump Love it! 🍿", "context": {"type": "tweet", "id": "1836485253759705127", "text": "Let’s go!!!!", "author_id": "1796321241789313024", "author_username": "BiatchDulce"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1836522940395065456", "tweet_id": "1836523689006780631", "timestamp": "2024-09-18T21:52:23+00:00", "timestamp_unix": 1726696343, "type": "comment", "text": "@MeidasTouch <PERSON> had the Fed lower rates 3x while he was in office. MAGA crying about it now is hilarious.", "context": {"type": "tweet", "id": "1913425468901982546", "text": "Seems like <PERSON> is <PERSON>’s new Fauci.  <PERSON>, the lying loser, always needs a scapegoat to blame for his own piss poor decisions.\n@POTUS is an idiot", "author_id": "1692610121115807744", "author_username": "DovFreeman6486"}, "metrics": {"retweet_count": 66, "reply_count": 9, "like_count": 296, "quote_count": 1, "view_count": 5207}}, {"conversation_id": "1854603094505431291", "tweet_id": "1854605182153507323", "timestamp": "2024-11-07T19:21:46+00:00", "timestamp_unix": 1731007306, "type": "comment", "text": "@MSNBC A little too late.", "context": {"type": "tweet", "id": "1855013935771979918", "text": "@SarahRudolp @jboogiebrown @elvislver56 @TheOralBuffet @philly_nj @CarolynAILG @SpeakTruth911 @LeftyWilbury17 MSNBC: https://t.co/BUiM5ceF2u\n@SarahR<PERSON><PERSON><PERSON> @jboogiebrown @elvislver56 @TheOralBuffet @philly_nj @CarolynAILG @SpeakTruth911 @LeftyWilbury17", "author_id": "1324584614602616832", "author_username": "PMorris185"}, "metrics": {"retweet_count": 0, "reply_count": 6, "like_count": 81, "quote_count": 0, "view_count": 4914}}, {"conversation_id": "1919035088198897953", "tweet_id": "1919035809467916638", "timestamp": "2025-05-04T14:26:05+00:00", "timestamp_unix": 1746368765, "type": "comment", "text": "@Acyn A reminder that he appointed <PERSON> in the first place.", "context": {"type": "tweet", "id": "1919204180486484279", "text": "https://t.co/dBHeLNe3kY", "author_id": "1481284852880183301", "author_username": "joe4deadcat"}, "metrics": {"retweet_count": 4, "reply_count": 0, "like_count": 68, "quote_count": 0, "view_count": 1177}}, {"conversation_id": "1920450081494388921", "tweet_id": "1920452339007144157", "timestamp": "2025-05-08T12:14:52+00:00", "timestamp_unix": 1746706492, "type": "comment", "text": "@DomLovesNovels I sure hope he ends up in one.", "context": {"type": "tweet", "id": "1920450512954052689", "text": "@ArtCandee When <PERSON> is in a padded-cell in a maximum security psychiatric hospital he won't be able to post on <PERSON> any more.", "author_id": "1133057295762509830", "author_username": "DomLovesNovels"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 7, "quote_count": 0, "view_count": 145}}]}