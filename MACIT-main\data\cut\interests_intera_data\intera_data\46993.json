{"user_id": "46993", "interactions_count": 19, "interactions": [{"conversation_id": "1765371004392530134", "tweet_id": "1765410719145795801", "timestamp": "2024-03-06T16:14:49+00:00", "timestamp_unix": 1709741689, "type": "comment", "text": "@KobeissiLetter Sounds like nothing has changed.", "context": {"type": "tweet", "id": "1765702121180246139", "text": "Higher for longer is the new normal, never leave, never will... Those points just cemented a hawkish pause stance more than ever :0", "author_id": "1571796975523344384", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 443}}, {"conversation_id": "1768257613156749638", "tweet_id": "1768307261674500190", "timestamp": "2024-03-14T16:04:39+00:00", "timestamp_unix": 1710432279, "type": "comment", "text": "@StealthQE4 Why does the Fed need to increase rates if inflation has fallen?  That's like saying the fire has gotten a lot smaller but we need to douse it with even more water.  Can't you lower the pressure and still put it out?  No one is saying to turn off the hose completely.", "context": {"type": "tweet", "id": "1768454752642474178", "text": "Rates any higher and we'll have a super ultra mega boom.", "author_id": "109705753", "author_username": "NewLowObserver"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 153}}, {"conversation_id": "1768257613156749638", "tweet_id": "1768359828383269135", "timestamp": "2024-03-14T19:33:32+00:00", "timestamp_unix": 1710444812, "type": "comment", "text": "@kimestep @StealthQE4 “US Inflation Rate is at 3.15%, compared to 3.09% last month and 6.04% last year.” - YCharts\n\nEven by your measure, why would you raise rates if it has slowed?  And why keep at current levels if improving?", "context": {"type": "tweet", "id": "1768354248713003347", "text": "@mortgagetruth @StealthQE4 But inflation hasn't fallen. Its rate of increase is declining. But today's PPI number suggests inflation is sticky.", "author_id": "243683754", "author_username": "kimestep"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1819411043061203048", "tweet_id": "1819436399076270591", "timestamp": "2024-08-02T18:13:35+00:00", "timestamp_unix": 1722622415, "type": "comment", "text": "@PeterSchiff What happened to 9% mortgage rates?  Old news now?", "context": {"type": "tweet", "id": "1820098711843733840", "text": "Big if tru.", "author_id": "354462175", "author_username": "DerAchsenZeit"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 575}}, {"conversation_id": "1819711455953817776", "tweet_id": "1820144170918678557", "timestamp": "2024-08-04T17:06:01+00:00", "timestamp_unix": 1722791161, "type": "comment", "text": "@StealthQE4 Think I read stocks do well during pause then underperform once pivot.  GL", "context": {"type": "tweet", "id": "1820134777208643621", "text": "this...", "author_id": "1468296488111333379", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 63}}, {"conversation_id": "1835798900508336227", "tweet_id": "1836126970784158116", "timestamp": "2024-09-17T19:35:58+00:00", "timestamp_unix": 1726601758, "type": "comment", "text": "@GMillerMortgage @PeterSchiff HELOC relief coming…", "context": {"type": "tweet", "id": "1836093603648516359", "text": "@PeterSchiff All loans tied to prime drop tomorrow so that helps consumers", "author_id": "1712502959253176323", "author_username": "GMillerMortgage"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1836489275476422819", "tweet_id": "1836578399755866207", "timestamp": "2024-09-19T01:29:47+00:00", "timestamp_unix": 1726709387, "type": "comment", "text": "@EricFinnigan Another perk to lower rates is MBS runoff.  🤔", "context": {"type": "tweet", "id": "1837095777539162369", "text": "So.. the way this works in broken market economics with critical shortages\n\nThere will be a 3 month shark 🦈 buying frenzy 🏡…\n\n..prices will soar 📈 \n\nthe circuit breaker will hit ⚡️ and the market will go back into deep freeze 🧊 at a higher price level.", "author_id": "4821024195", "author_username": "de<PERSON><PERSON>_"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 609}}, {"conversation_id": "1843255098966995171", "tweet_id": "1843319281133728079", "timestamp": "2024-10-07T15:55:38+00:00", "timestamp_unix": 1728316538, "type": "comment", "text": "@AustinWhittRE It became a home price story a few months ago.  People stopped focusing entirely on rates after they appeared to peak and fall, and perhaps looked at the big picture.", "context": {"type": "tweet", "id": "1843620480122794227", "text": "Well rates. Went up", "author_id": "1537991301735403520", "author_username": "AntiWarParty1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 187}}, {"conversation_id": "1843760326992679199", "tweet_id": "1843781523818066009", "timestamp": "2024-10-08T22:32:25+00:00", "timestamp_unix": 1728426745, "type": "comment", "text": "@StealthQE4 The premise of this article isn't even accurate.  \n\nFed cuts aren't supposed to push mortgage rates lower.\n\nI thought the bond market front runs the Fed?", "context": {"type": "tweet", "id": "1844569414513066312", "text": "We just had a strong jobs report which pushed the 10-year treasury yield to 4%, which sorry to say means higher mortgage rates (for now)\n\nStrong economic data, combined with foreseeable inflation = rates📈", "author_id": "1612279651178807298", "author_username": "samnyse"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 277}}, {"conversation_id": "1850980772917674460", "tweet_id": "1851002464935329845", "timestamp": "2024-10-28T20:45:52+00:00", "timestamp_unix": 1730148352, "type": "comment", "text": "@dougboneparth The problem was they went out of order.", "context": {"type": "tweet", "id": "1851305336650731626", "text": "Can confirm.", "author_id": "3753930313", "author_username": "amanda<PERSON>_fuller"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 575}}, {"conversation_id": "1854148881052774532", "tweet_id": "1854188719944953986", "timestamp": "2024-11-06T15:46:54+00:00", "timestamp_unix": 1730908014, "type": "comment", "text": "@texasrunnerDFW Higher rates indicate a stronger economy.  Data needs to support that, not an election.", "context": {"type": "tweet", "id": "1854349251733487989", "text": "Bond market will dictate mtg rates not the Fed.", "author_id": "804210827083612160", "author_username": "JohnRaven<PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 238}}, {"conversation_id": "1854600790113161496", "tweet_id": "1854602865198653672", "timestamp": "2024-11-07T19:12:34+00:00", "timestamp_unix": 1731006754, "type": "comment", "text": "@byHeatherLong And the 30-year fixed is back in the 6s.  But they have nothing to with each other.  Good times.", "context": {"type": "tweet", "id": "1855207421318172983", "text": "<PERSON> waited a year to start raising rates because he's a trump appointee. He should have been replaced when <PERSON><PERSON> took office.", "author_id": "990915242526957569", "author_username": "<PERSON>_Fleming321"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 788}}, {"conversation_id": "1854601062017384678", "tweet_id": "1854643812955508785", "timestamp": "2024-11-07T21:55:17+00:00", "timestamp_unix": 1731016517, "type": "comment", "text": "@TheMaverickWS Bonds were oversold.", "context": {"type": "tweet", "id": "1854815656778092547", "text": "So $nail may go lower wish I had more $tsla so many paying at $300 or higher I am loading below $300 yes I know almost there.  Pruning anything that looks likely to go down short term for $tsla today. <PERSON><PERSON> is my #2 for open positions unrealized gains behind $blx 3/4 year hold", "author_id": "1837667397261492224", "author_username": "JamesNotBo98595"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 200}}, {"conversation_id": "1869484027650150829", "tweet_id": "1869538871845011641", "timestamp": "2024-12-19T00:22:56+00:00", "timestamp_unix": 1734567776, "type": "comment", "text": "@MacroEdgeRes “Ticks up” is a bit of an understatement.", "context": {"type": "tweet", "id": "1869835920041796064", "text": "Wut????", "author_id": "166932628", "author_username": "RedFebtober"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 8, "quote_count": 0, "view_count": 689}}, {"conversation_id": "1869558483445829884", "tweet_id": "1869842733642461584", "timestamp": "2024-12-19T20:30:22+00:00", "timestamp_unix": 1734640222, "type": "comment", "text": "@notmrmanziel Rates went up about 0.25%.  If you could only afford a home purchase with a rate 0.25% lower then well…", "context": {"type": "tweet", "id": "1870117861471010990", "text": "No. House prices are cooked. Affordability will slowly return.", "author_id": "804210827083612160", "author_username": "JohnRaven<PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 449}}, {"conversation_id": "1882940061832056941", "tweet_id": "1883000483272638498", "timestamp": "2025-01-25T03:54:34+00:00", "timestamp_unix": 1737777274, "type": "comment", "text": "@GayBearRes Post-inauguration is now.  Let’s let that last one simmer.", "context": {"type": "tweet", "id": "1893204817725571461", "text": "@ThinkAppraiser Ok great news for you, but the low transaction number was the entire context for the poll that I showed.", "author_id": "1634307214335778816", "author_username": "GayBearRes"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 183}}, {"conversation_id": "1882940061832056941", "tweet_id": "1883025483753001053", "timestamp": "2025-01-25T05:33:54+00:00", "timestamp_unix": 1737783234, "type": "comment", "text": "@GayBearRes It was four days ago.  It’s not my thesis so merely pointing out the timing.", "context": {"type": "tweet", "id": "1883000651032281381", "text": "@mortgagetruth lol ok dude, how many more weeks?", "author_id": "1634307214335778816", "author_username": "GayBearRes"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 74}}, {"conversation_id": "1887480366115537336", "tweet_id": "1887519580597125200", "timestamp": "2025-02-06T15:11:51+00:00", "timestamp_unix": **********, "type": "comment", "text": "@WarrenPies Spreads can solve much of the shortfall, but they've created even more volatility and defensiveness in pricing.", "context": {"type": "tweet", "id": "1887948112167321895", "text": "Good stuff. Glad <PERSON><PERSON> got appointed…", "author_id": "1757439705052860416", "author_username": "AndersonIv83141"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 342}}, {"conversation_id": "1887635157831610703", "tweet_id": "1887881947172532594", "timestamp": "2025-02-07T15:11:46+00:00", "timestamp_unix": **********, "type": "comment", "text": "@FairweatherPhD It’s more the uncertainty around his policies causing spreads to stay bloated or even widen, despite falling yields.", "context": {"type": "tweet", "id": "1887804093109862569", "text": "This is simply untrue. \n\nMortgages Rates have hit their lowest point of 2025 this week. https://t.co/XB2EXkgHbn", "author_id": "1518906065026437121", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 62}}]}