{"user_id": "1565688833768833025", "interactions_count": 14, "interactions": [{"conversation_id": "1836487956200071296", "tweet_id": "1836489450093973658", "timestamp": "2024-09-18T19:36:19+00:00", "timestamp_unix": 1726688179, "type": "comment", "text": "@13isLucky13 I can’t believe <PERSON> was so savage in his response as to why he slashed rates", "context": {"type": "tweet", "id": "1836489258045190518", "text": "@brad_rando That’s that’s a big rate cut 💯", "author_id": "1599879585881022495", "author_username": "13isLucky13"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 29}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836491201639551187", "timestamp": "2024-09-18T19:43:17+00:00", "timestamp_unix": 1726688597, "type": "comment", "text": "@Sahin_OG X makes the future of free speech and citizen journalism possible", "context": {"type": "tweet", "id": "1836490941336887720", "text": "@brad_rando <PERSON>o Brad News Network is the future 🔥", "author_id": "1445140710567542790", "author_username": "Sahin_OG"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836492370520449265", "timestamp": "2024-09-18T19:47:56+00:00", "timestamp_unix": 1726688876, "type": "quote", "text": "@InveXtigator We have an exclusive interview with <PERSON> on the RBNN", "context": {"type": "tweet", "id": "1836487956200071296", "text": "#BreakingNews \nIts #NationalCheeseburgerDay and a slow news day so far\n-<PERSON> cuts interest rates by 50 bps for the first time in 4 years\n-Passion of the Christ 2 Trailer has been released\nand \n-What is <PERSON><PERSON> doing with all that lube ?\n\nlets find out https://t.co/O82UajwJOb", "author_id": "1565688833768833025", "author_username": "brad_rando"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 117}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836493582431121631", "timestamp": "2024-09-18T19:52:45+00:00", "timestamp_unix": 1726689165, "type": "comment", "text": "@afristats_polls I tweaked it slightly so I can claim the whole thing as original work https://t.co/xzXFr38pBr", "context": {"type": "tweet", "id": "1836537782199599416", "text": "@tweetsforyoul Never seen them in my life but here’s what’s trending in the news today", "author_id": "1565688833768833025", "author_username": "brad_rando"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836501449213407417", "timestamp": "2024-09-18T20:24:00+00:00", "timestamp_unix": 1726691040, "type": "comment", "text": "@JohnFelix49 Ya they found dudes in house locked up like this also https://t.co/OSfmwswqkE", "context": {"type": "tweet", "id": "1836500989525786858", "text": "@brad_rando <PERSON>?", "author_id": "1265301154784821250", "author_username": "JohnFelix49"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836501488320872773", "timestamp": "2024-09-18T20:24:10+00:00", "timestamp_unix": 1726691050, "type": "comment", "text": "@JohnFelix49 Okay that’s a lie", "context": {"type": "tweet", "id": "1836501449213407417", "text": "@JohnFelix49 Ya they found dudes in house locked up like this also https://t.co/OSfmwswqkE", "author_id": "1565688833768833025", "author_username": "brad_rando"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836504254086869422", "timestamp": "2024-09-18T20:35:09+00:00", "timestamp_unix": 1726691709, "type": "comment", "text": "@ReidMatthew7 The Va loan on the house came with a shirt 🤝", "context": {"type": "tweet", "id": "1836503850087334192", "text": "@brad_rando Dude, I swear I have that exact shirt! Sleeves cut off and all!", "author_id": "1596508018304327682", "author_username": "ReidMatthew7"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 18}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836505349458682280", "timestamp": "2024-09-18T20:39:30+00:00", "timestamp_unix": 1726691970, "type": "quote", "text": "@KSJ_Kreative I randomly do the news I need to do it more often but whatever. 🤷‍♂️ 😂", "context": {"type": "tweet", "id": "1836487956200071296", "text": "#BreakingNews \nIts #NationalCheeseburgerDay and a slow news day so far\n-<PERSON> cuts interest rates by 50 bps for the first time in 4 years\n-Passion of the Christ 2 Trailer has been released\nand \n-What is <PERSON><PERSON> doing with all that lube ?\n\nlets find out https://t.co/O82UajwJOb", "author_id": "1565688833768833025", "author_username": "brad_rando"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 41}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836536258602144121", "timestamp": "2024-09-18T22:42:19+00:00", "timestamp_unix": 1726699339, "type": "quote", "text": "@greg16676935420 Make sure you catch the news today Greg", "context": {"type": "tweet", "id": "1836487956200071296", "text": "#BreakingNews \nIts #NationalCheeseburgerDay and a slow news day so far\n-<PERSON> cuts interest rates by 50 bps for the first time in 4 years\n-Passion of the Christ 2 Trailer has been released\nand \n-What is <PERSON><PERSON> doing with all that lube ?\n\nlets find out https://t.co/O82UajwJOb", "author_id": "1565688833768833025", "author_username": "brad_rando"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 188}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836537782199599416", "timestamp": "2024-09-18T22:48:23+00:00", "timestamp_unix": 1726699703, "type": "quote", "text": "@tweetsforyoul Never seen them in my life but here’s what’s trending in the news today", "context": {"type": "tweet", "id": "1836487956200071296", "text": "#BreakingNews \nIts #NationalCheeseburgerDay and a slow news day so far\n-<PERSON> cuts interest rates by 50 bps for the first time in 4 years\n-Passion of the Christ 2 Trailer has been released\nand \n-What is <PERSON><PERSON> doing with all that lube ?\n\nlets find out https://t.co/O82UajwJOb", "author_id": "1565688833768833025", "author_username": "brad_rando"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 3404}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836541705509896367", "timestamp": "2024-09-18T23:03:58+00:00", "timestamp_unix": 1726700638, "type": "comment", "text": "@<PERSON><PERSON><PERSON><PERSON> I’m trying to shake these up and ensure I’m providing some news but maybe more humor 🤷‍♂️", "context": {"type": "tweet", "id": "1836540897317785812", "text": "@brad_rando I‘m a lil bit overwhelmed with this news. \nEnjoyed as ever.", "author_id": "2180162890", "author_username": "<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836555903178596691", "timestamp": "2024-09-19T00:00:23+00:00", "timestamp_unix": 1726704023, "type": "comment", "text": "@aks<PERSON><PERSON><PERSON><PERSON> I wasn’t expecting this news today myself", "context": {"type": "tweet", "id": "1836555526668165144", "text": "@brad_rando Breaking news", "author_id": "820161757104787458", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836576199537037484", "timestamp": "2024-09-19T01:21:02+00:00", "timestamp_unix": 1726708862, "type": "comment", "text": "@ursister366 I like to share the news 🤷‍♂️", "context": {"type": "tweet", "id": "1836575627756879963", "text": "@brad_rando Thank you for sharing this", "author_id": "1258363561375600640", "author_username": "khan83770"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1836487956200071296", "tweet_id": "1836752477686845861", "timestamp": "2024-09-19T13:01:30+00:00", "timestamp_unix": 1726750890, "type": "comment", "text": "@WanderStudios6 The market is responding well to the rate cuts 💪", "context": {"type": "tweet", "id": "1836748857763352722", "text": "@brad_rando Oh my God! I can't stop laughing 🤣🤣🤣", "author_id": "1740350652143038464", "author_username": "WanderStudios6"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 5}}]}