#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行所有interests数据的处理脚本
支持批量处理、断点续传、进度监控
"""

import argparse
import json
import logging
import os
import sys
import time
from pathlib import Path

from logger_config import setup_logger

# 设置日志
logger = setup_logger('interests_batch_processor')


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='批量处理所有interests数据')
    
    parser.add_argument(
        '--batch_size',
        type=int,
        default=50,
        help='每批处理的用户数量，默认50'
    )
    
    parser.add_argument(
        '--start_from',
        type=int,
        default=0,
        help='从第几个用户开始处理（用于断点续传），默认0'
    )
    
    parser.add_argument(
        '--max_users',
        type=int,
        default=None,
        help='最大处理用户数量，默认处理所有用户'
    )
    
    parser.add_argument(
        '--delay',
        type=float,
        default=3.0,
        help='每个用户处理之间的延迟（秒），默认3秒'
    )
    
    parser.add_argument(
        '--skip_existing',
        action='store_true',
        help='跳过已经处理过的用户'
    )
    
    return parser.parse_args()


def get_interests_user_list():
    """获取所有interests用户ID列表"""
    interests_data_dir = Path("data/cut/interests_intera_data/intera_data")
    
    if not interests_data_dir.exists():
        logger.error(f"interests数据目录不存在: {interests_data_dir}")
        return []
    
    json_files = list(interests_data_dir.glob("*.json"))
    user_ids = [f.stem for f in json_files]
    
    logger.info(f"发现 {len(user_ids)} 个interests用户数据文件")
    return sorted(user_ids)


def check_user_processed(user_id):
    """检查用户是否已经处理过"""
    output_file = Path("interests_output") / user_id / "interaction_0.json"
    return output_file.exists()


def run_single_user(user_id):
    """处理单个用户"""
    import subprocess
    
    cmd = [
        sys.executable, 
        "process_interests_data.py", 
        "--user_id", user_id
    ]
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            logger.info(f"用户 {user_id} 处理成功")
            return True
        else:
            logger.error(f"用户 {user_id} 处理失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"用户 {user_id} 处理超时")
        return False
    except Exception as e:
        logger.error(f"用户 {user_id} 处理异常: {e}")
        return False


def save_progress(processed_users, failed_users, progress_file="interests_progress.json"):
    """保存处理进度"""
    progress_data = {
        "processed_users": processed_users,
        "failed_users": failed_users,
        "timestamp": time.time()
    }
    
    with open(progress_file, 'w', encoding='utf-8') as f:
        json.dump(progress_data, f, ensure_ascii=False, indent=2)


def load_progress(progress_file="interests_progress.json"):
    """加载处理进度"""
    if not Path(progress_file).exists():
        return [], []
    
    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)
        
        return progress_data.get("processed_users", []), progress_data.get("failed_users", [])
    except Exception as e:
        logger.warning(f"加载进度文件失败: {e}")
        return [], []


def main():
    """主函数"""
    args = parse_args()
    
    # 获取所有interests用户
    all_users = get_interests_user_list()
    
    if not all_users:
        logger.error("未找到任何interests用户数据")
        return
    
    # 应用起始位置和最大用户数限制
    if args.start_from > 0:
        all_users = all_users[args.start_from:]
        logger.info(f"从第 {args.start_from + 1} 个用户开始处理")
    
    if args.max_users:
        all_users = all_users[:args.max_users]
        logger.info(f"限制处理前 {args.max_users} 个用户")
    
    # 加载之前的进度
    processed_users, failed_users = load_progress()
    
    # 过滤已处理的用户
    if args.skip_existing:
        remaining_users = []
        for user_id in all_users:
            if user_id not in processed_users and not check_user_processed(user_id):
                remaining_users.append(user_id)
        all_users = remaining_users
        logger.info(f"跳过已处理用户，剩余 {len(all_users)} 个用户待处理")
    
    logger.info(f"开始批量处理 {len(all_users)} 个interests用户")
    
    # 创建输出目录
    Path("interests_output").mkdir(exist_ok=True)
    
    # 批量处理
    total_processed = 0
    total_failed = 0
    
    for i in range(0, len(all_users), args.batch_size):
        batch_users = all_users[i:i + args.batch_size]
        batch_num = i // args.batch_size + 1
        total_batches = (len(all_users) + args.batch_size - 1) // args.batch_size
        
        logger.info(f"开始处理第 {batch_num}/{total_batches} 批，包含 {len(batch_users)} 个用户")
        
        batch_processed = 0
        batch_failed = 0
        
        for j, user_id in enumerate(batch_users, 1):
            logger.info(f"处理用户 {user_id} ({j}/{len(batch_users)}) - 总进度: {total_processed + batch_processed + 1}/{len(all_users)}")
            
            success = run_single_user(user_id)
            
            if success:
                batch_processed += 1
                processed_users.append(user_id)
            else:
                batch_failed += 1
                failed_users.append(user_id)
            
            # 保存进度
            save_progress(processed_users, failed_users)
            
            # 延迟
            if j < len(batch_users):  # 不是最后一个用户
                time.sleep(args.delay)
        
        total_processed += batch_processed
        total_failed += batch_failed
        
        logger.info(f"第 {batch_num} 批处理完成: 成功 {batch_processed}, 失败 {batch_failed}")
        
        # 批次间稍长延迟
        if i + args.batch_size < len(all_users):
            time.sleep(5)
    
    # 最终统计
    logger.info("="*60)
    logger.info("interests数据批量处理完成!")
    logger.info(f"总计处理: {len(all_users)} 个用户")
    logger.info(f"成功: {total_processed} 个用户")
    logger.info(f"失败: {total_failed} 个用户")
    
    if total_processed + total_failed > 0:
        success_rate = total_processed / (total_processed + total_failed) * 100
        logger.info(f"成功率: {success_rate:.1f}%")
    
    logger.info(f"结果保存在: interests_output 文件夹")
    logger.info("="*60)
    
    # 显示失败的用户
    if failed_users:
        logger.warning(f"以下 {len(failed_users)} 个用户处理失败:")
        for user_id in failed_users[-10:]:  # 只显示最后10个失败的
            logger.warning(f"  - {user_id}")
        if len(failed_users) > 10:
            logger.warning(f"  ... 还有 {len(failed_users) - 10} 个失败用户")


if __name__ == "__main__":
    main()
