@echo off
echo ========================================
echo MACIT框架 - interests数据批量处理
echo ========================================

REM 激活anaconda环境
echo 正在激活MACIT虚拟环境...
call conda activate MACIT
if errorlevel 1 (
    echo 错误：无法激活MACIT虚拟环境
    echo 请确保已经创建了MACIT虚拟环境
    pause
    exit /b 1
)

echo 虚拟环境激活成功！

REM 检查Python环境
echo 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误：Python不可用
    pause
    exit /b 1
)

REM 检查必要的包
echo 检查必要的Python包...
python -c "import openai, camel" 2>nul
if errorlevel 1 (
    echo 错误：缺少必要的Python包
    echo 请运行: pip install openai camel-ai
    pause
    exit /b 1
)

echo 环境检查完成！

REM 创建输出目录
echo 创建输出目录...
if not exist "interests_output" mkdir interests_output

REM 运行interests数据处理
echo ========================================
echo 开始处理interests数据...
echo ========================================

python run_interests_data.py --batch_size 10 --delay 3.0 --skip_existing

echo ========================================
echo interests数据处理完成！
echo ========================================

REM 显示结果统计
echo 正在统计处理结果...
python -c "
import os
from pathlib import Path

output_dir = Path('interests_output')
if output_dir.exists():
    processed_users = len([d for d in output_dir.iterdir() if d.is_dir() and (d / 'interaction_0.json').exists()])
    print(f'成功处理的用户数量: {processed_users}')
else:
    print('输出目录不存在')
"

echo ========================================
echo 处理完成！结果保存在 interests_output 文件夹中
echo ========================================

pause
