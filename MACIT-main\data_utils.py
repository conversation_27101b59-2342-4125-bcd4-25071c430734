#!/usr/bin/env python
# -*- coding: utf-8 -*-

# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========

"""
数据加载和处理模块，用于加载用户画像、交互数据和话题背景信息
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

from macit_config import USER_PROFILES_PATH, CUT_DESCRIPTION_PATH, INTERA_DATA_DIR

# 配置日志
logger = logging.getLogger(__name__)


def load_json_file(file_path: Union[str, Path]) -> Any:
    """
    加载JSON文件

    Args:
        file_path: JSON文件路径

    Returns:
        加载的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON文件 {file_path} 失败: {e}")
        raise


def load_text_file(file_path: Union[str, Path]) -> str:
    """
    加载文本文件

    Args:
        file_path: 文本文件路径

    Returns:
        文本内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"加载文本文件 {file_path} 失败: {e}")
        raise


def load_user_profiles() -> Dict[str, Dict]:
    """
    加载用户画像数据

    Returns:
        用户ID到用户画像的映射字典
    """
    user_profiles = load_json_file(USER_PROFILES_PATH)
    # 将用户画像数据转换为以用户名为键的字典
    user_profiles_dict = {}
    for profile in user_profiles:
        username = profile.get("username")
        if username:
            user_profiles_dict[username] = profile

    logger.info(f"成功加载 {len(user_profiles_dict)} 个用户画像")
    return user_profiles_dict


def load_topic_background() -> str:
    """
    加载话题背景信息

    Returns:
        话题背景文本
    """
    background = load_text_file(CUT_DESCRIPTION_PATH)
    logger.info("成功加载话题背景信息")
    return background


def load_interaction_data(user_id: str, use_dei_data: bool = False, use_interests_data: bool = False) -> Dict:
    """
    加载特定用户的交互数据

    Args:
        user_id: 用户ID
        use_dei_data: 是否使用DEI数据路径
        use_interests_data: 是否使用interests数据路径

    Returns:
        用户的交互数据
    """
    if use_dei_data:
        # 使用DEI数据路径
        file_path = Path("data/cut/DEI_intera_data/intera_data") / f"{user_id}.json"
    elif use_interests_data:
        # 使用interests数据路径
        file_path = Path("data/cut/interests_intera_data/intera_data") / f"{user_id}.json"
    else:
        # 使用默认路径
        file_path = INTERA_DATA_DIR / f"{user_id}.json"

    interaction_data = load_json_file(file_path)
    logger.info(f"成功加载用户 {user_id} 的交互数据，共 {len(interaction_data.get('interactions', []))} 条交互")
    return interaction_data


def get_interaction_sample(interaction_data: Dict, index: int = 0) -> Dict:
    """
    获取特定索引的交互样本

    Args:
        interaction_data: 交互数据
        index: 样本索引

    Returns:
        交互样本
    """
    interactions = interaction_data.get("interactions", [])
    if not interactions or index >= len(interactions):
        logger.error(f"索引 {index} 超出交互数据范围")
        raise IndexError(f"索引 {index} 超出交互数据范围")

    return interactions[index]


def get_all_user_ids() -> List[str]:
    """
    获取所有用户ID

    Returns:
        用户ID列表
    """
    user_ids = []
    for file_path in INTERA_DATA_DIR.glob("*.json"):
        user_id = file_path.stem
        user_ids.append(user_id)

    logger.info(f"找到 {len(user_ids)} 个用户ID")
    return user_ids


def format_user_profile(profile: Dict) -> str:
    """
    格式化用户画像为字符串

    Args:
        profile: 用户画像数据

    Returns:
        格式化后的用户画像字符串
    """
    if not profile:
        return "无用户画像数据"

    formatted = []
    formatted.append(f"用户名: {profile.get('username', 'Unknown')}")
    formatted.append(f"真实姓名: {profile.get('realname', 'Unknown')}")
    formatted.append(f"简介: {profile.get('bio', 'No bio')}")
    formatted.append(f"人物描述: {profile.get('persona', 'No persona')}")

    # 基本信息
    formatted.append(f"年龄: {profile.get('age', 'Unknown')}")
    formatted.append(f"性别: {profile.get('gender', 'Unknown')}")
    formatted.append(f"MBTI: {profile.get('mbti', 'Unknown')}")
    formatted.append(f"国家: {profile.get('country', 'Unknown')}")
    formatted.append(f"职业: {profile.get('profession', 'Unknown')}")

    # 兴趣话题
    topics = profile.get('interested_topics', [])
    if topics:
        formatted.append(f"感兴趣的话题: {', '.join(topics)}")

    # 影响力指标
    influence = profile.get('influence_metrics', {})
    if influence:
        formatted.append("影响力指标:")
        formatted.append(f"  - 点赞数: {influence.get('like_count', 0)}")
        formatted.append(f"  - 转发数: {influence.get('retweet_count', 0)}")
        formatted.append(f"  - 影响力得分: {influence.get('influence_score', 0)}")

    # 认知特征
    cognitive = profile.get('cognitive_profile', {})
    if cognitive:
        formatted.append("认知特征:")
        for key, value in cognitive.items():
            if key != "opinion" and isinstance(value, dict):
                formatted.append(f"  - {key}: {value.get('type', '')} - {value.get('value', '')}")

    # 观点
    opinions = cognitive.get('opinion', []) if cognitive else []
    if opinions:
        formatted.append("观点:")
        for i, opinion in enumerate(opinions, 1):
            viewpoint = next((v for k, v in opinion.items() if k.startswith('viewpoint_')), "")
            support = opinion.get('type_support_levels', "")
            if viewpoint and support:
                formatted.append(f"  {i}. {viewpoint} - {support}")

    return "\n".join(formatted)


def find_user_profile_by_id(user_id: str, user_profiles: Dict[str, Dict]) -> Optional[Dict]:
    """
    通过用户ID查找用户画像

    Args:
        user_id: 用户ID
        user_profiles: 用户画像字典

    Returns:
        用户画像，如果未找到则返回None
    """
    # 首先尝试直接匹配
    if user_id in user_profiles:
        return user_profiles[user_id]

    # 如果未找到，尝试从交互数据中获取用户名
    try:
        interaction_data = load_interaction_data(user_id)
        username = interaction_data.get("username")
        if username and username in user_profiles:
            return user_profiles[username]
    except Exception:
        pass

    logger.warning(f"未找到用户 {user_id} 的画像")
    return None