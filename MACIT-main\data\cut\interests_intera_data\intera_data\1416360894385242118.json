{"user_id": "1416360894385242118", "interactions_count": 19, "interactions": [{"conversation_id": "1912812081046241367", "tweet_id": "1912814490162184318", "timestamp": "2025-04-17T10:24:47+00:00", "timestamp_unix": 1744885487, "type": "comment", "text": "@DeItaone <PERSON> demonstrated his political alignment by supporting the government at the time with a 50bps rate cut in September. The Fed is a political institution.", "context": {"type": "tweet", "id": "1915886553982763290", "text": "Man, the dollar was just starting to get a bid too...\n\nMore jet fuel for gold though", "author_id": "427245443", "author_username": "infraa_1"}, "metrics": {"retweet_count": 2, "reply_count": 8, "like_count": 65, "quote_count": 0, "view_count": 3998}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912819199803600918", "timestamp": "2025-04-17T10:43:30+00:00", "timestamp_unix": 1744886610, "type": "comment", "text": "@Facts0601 @DeItaone It was undoubtedly a foolish appointment and another foolish one was <PERSON>.", "context": {"type": "tweet", "id": "1912815000072208531", "text": "@emrahaktasss95 @DeItaone Trump appointed him.", "author_id": "1220592542988763137", "author_username": "Facts0601"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 4, "quote_count": 0, "view_count": 310}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912825174988108235", "timestamp": "2025-04-17T11:07:14+00:00", "timestamp_unix": 1744888034, "type": "comment", "text": "@GoalLineFade69 @Facts0601 @DeItaone If you ignore inflation, it becomes damaging. https://t.co/XDLKju0azN", "context": {"type": "tweet", "id": "1912821006546125051", "text": "@emrahaktasss95 @Facts0601 @DeItaone Powell jacked rates under <PERSON><PERSON>, was that politically motivated to hurt democrats?", "author_id": "1319484752349614081", "author_username": "GoalLineFade69"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 112}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912830385236447237", "timestamp": "2025-04-17T11:27:57+00:00", "timestamp_unix": 1744889277, "type": "comment", "text": "@GoalLineFade69 @Facts0601 @DeItaone If all it takes is looking at the data, then a student who took Econ 101 is qualified to be Fed Chair. And if you’re going to just sit on the fence, then you end QT. you don’t do QE, you don’t do QT, you just “watch the data.  😉", "context": {"type": "tweet", "id": "1912828270438973912", "text": "@emrahaktasss95 @Facts0601 @DeItaone Powell waiting to see data that actually includes the new tariffs isn't political, it's just data dependent.", "author_id": "1319484752349614081", "author_username": "GoalLineFade69"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 90}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912833909605163154", "timestamp": "2025-04-17T11:41:57+00:00", "timestamp_unix": 1744890117, "type": "comment", "text": "@GoalLineFade69 @Facts0601 @DeItaone Is there an inflation risk? 😅 Seems like you haven’t seen the recent monthly data. Consider the disinflation risk and the current rates also bring a recession risk.", "context": {"type": "tweet", "id": "1912832860475507067", "text": "@emrahaktasss95 @Facts0601 @DeItaone QT helps fight inflation, normalizes fed balance sheet and compliments the current rate level. It's also carefully draining liquidity from system. \n\nIt's appropriate to be doing that now imo.", "author_id": "1319484752349614081", "author_username": "GoalLineFade69"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 77}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912834819634978908", "timestamp": "2025-04-17T11:45:34+00:00", "timestamp_unix": 1744890334, "type": "comment", "text": "@GoalLineFade69 @Facts0601 @DeItaone I really wanted <PERSON><PERSON> to be elected so they could add to my wealth by strictly applying Keynesian economics and the MMT handbook.😅", "context": {"type": "tweet", "id": "1912833909605163154", "text": "@GoalLineFade69 @Facts0601 @DeItaone Is there an inflation risk? 😅 Seems like you haven’t seen the recent monthly data. Consider the disinflation risk and the current rates also bring a recession risk.", "author_id": "1416360894385242118", "author_username": "emrahaktasss95"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 69}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912843912420409482", "timestamp": "2025-04-17T12:21:42+00:00", "timestamp_unix": 1744892502, "type": "comment", "text": "@GoalLineFade69 @Facts0601 @DeItaone The Fed has done forward guidance before it can do it again, just like it did in September 😊 <PERSON> will take care of it 😀", "context": {"type": "tweet", "id": "1912835726133051769", "text": "@emrahaktasss95 @Facts0601 @DeItaone Bond market says no rate cuts", "author_id": "1319484752349614081", "author_username": "GoalLineFade69"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 39}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912854620595957990", "timestamp": "2025-04-17T13:04:15+00:00", "timestamp_unix": 1744895055, "type": "comment", "text": "@GoalLineFade69 @Facts0601 @DeItaone Yeah, sure, no risks at the ECB either. Anyway, good luck with your logic.", "context": {"type": "tweet", "id": "1912847382724612393", "text": "@emrahaktasss95 @Facts0601 @DeItaone Forward guidance in this uncertain economic environment doesn't make sense to me.", "author_id": "1319484752349614081", "author_username": "GoalLineFade69"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 28}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912882931439116333", "timestamp": "2025-04-17T14:56:45+00:00", "timestamp_unix": 1744901805, "type": "comment", "text": "@GoalLineFade69 @Facts0601 @DeItaone The ECB’s growth forecast is 1.3%, but what’s the Fed’s year-end projection? It’s below 1 —Fed’s <PERSON> said so just yesterday. Europe has already surrendered to socialism, and the Fed is the last fortress of socialism.", "context": {"type": "tweet", "id": "1912861121133924858", "text": "@emrahaktasss95 @Facts0601 @DeItaone You want to be like Europe? Look at their growth rate.", "author_id": "1319484752349614081", "author_username": "GoalLineFade69"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 33}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912883751064916074", "timestamp": "2025-04-17T15:00:00+00:00", "timestamp_unix": 1744902000, "type": "comment", "text": "@GoalLineFade69 @Facts0601 @DeItaone Before the policy uncertainty, the ECB acted and brought inflation down. What did <PERSON> do? Nothing. It’s going to be the same story again — a continuation of incompetence. https://t.co/s9r81ZO4wl", "context": {"type": "tweet", "id": "1912882931439116333", "text": "@GoalLineFade69 @Facts0601 @DeItaone The ECB’s growth forecast is 1.3%, but what’s the Fed’s year-end projection? It’s below 1 —Fed’s <PERSON> said so just yesterday. Europe has already surrendered to socialism, and the Fed is the last fortress of socialism.", "author_id": "1416360894385242118", "author_username": "emrahaktasss95"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 41}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912904081074438474", "timestamp": "2025-04-17T16:20:47+00:00", "timestamp_unix": 1744906847, "type": "comment", "text": "@OGUnfluencer @Facts0601 @DeItaone Lagarde achieved this long ago, but <PERSON> failed to do so before <PERSON> even came into office.", "context": {"type": "tweet", "id": "1912873260439548092", "text": "@emrahaktasss95 @Facts0601 @DeItaone Powell may go down as the most effective Fed Chair in our history. A soft landing was being engineered, but then <PERSON> happened", "author_id": "*********", "author_username": "OGUnfluencer"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 59}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912940832782516363", "timestamp": "2025-04-17T18:46:49+00:00", "timestamp_unix": **********, "type": "comment", "text": "The primary duty and responsibility of central banks is to ensure price stability — and <PERSON><PERSON><PERSON> succeeded in doing that. Meanwhile, the FTSE represents a non-EU country where the Labour Party has significant influence — a fully socialist structure. The EU’s foolish bureaucracy and semi-socialist framework lag far behind the U.S. in terms of productivity.", "context": {"type": "tweet", "id": "1912914633779749332", "text": "@emrahaktasss95 @Facts0601 @DeItaone I think that’s demonstrably false. They are doing better in the recent short term on inflation, but by almost every other metric US has had a much better recovery. EU GDP actually shrank in 2022. They had a recession. The 5yr return on the FTSE is just 17.6% v 83.6% on the SPX", "author_id": "*********", "author_username": "OGUnfluencer"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 49}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912941410539495544", "timestamp": "2025-04-17T18:49:07+00:00", "timestamp_unix": **********, "type": "comment", "text": "@OGUnfluencer @Facts0601 @DeItaone The rise in the SPX is driven by the revolution in artificial intelligence, and the surge in productivity will continue rapidly", "context": {"type": "tweet", "id": "1912940832782516363", "text": "The primary duty and responsibility of central banks is to ensure price stability — and <PERSON><PERSON><PERSON> succeeded in doing that. Meanwhile, the FTSE represents a non-EU country where the Labour Party has significant influence — a fully socialist structure. The EU’s foolish bureaucracy and semi-socialist framework lag far behind the U.S. in terms of productivity.", "author_id": "1416360894385242118", "author_username": "emrahaktasss95"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 57}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912943836545843559", "timestamp": "2025-04-17T18:58:46+00:00", "timestamp_unix": **********, "type": "comment", "text": "@hilpco @DeItaone I don’t live in the U.S., I’m a citizen of Cyprus — but I find <PERSON>’s policies logical. So no, I don’t need to be a bootlicker, my friend.", "context": {"type": "tweet", "id": "1912850473842847849", "text": "@emrahaktasss95 @DeItaone Whenever someone doesn’t do whatever <PERSON> wants but does his job, <PERSON> attacks him.  <PERSON> is doing his job.  So many Trump simps here wanting <PERSON> to get his childish ways on everything.", "author_id": "1864705136301334528", "author_username": "hilpco"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 45}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912944952918855740", "timestamp": "2025-04-17T19:03:12+00:00", "timestamp_unix": **********, "type": "comment", "text": "@Kuzeydeki_Kral1 @DeItaone If you look at it through the lens of the quantity theory of money, the Fed is already tight enough. If you want to challenge two countries, the M2 charts will show you the way.", "context": {"type": "tweet", "id": "1912850807336083528", "text": "@emrahaktasss95 @DeItaone He know it’s too early to cut rates. Erdogan also forced central bank to cut rates too early now Turkey has 46% interest rates as well as 65% inflation", "author_id": "*********", "author_username": "Kuzeydeki_Kral1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 49}}, {"conversation_id": "1912812081046241367", "tweet_id": "1912949626749202763", "timestamp": "2025-04-17T19:21:46+00:00", "timestamp_unix": **********, "type": "comment", "text": "@JamieFoll<PERSON> @Facts0601 @DeItaone At least some people have fact and logic that can be broken — others have nothing to break in the first place. 😀😀😉", "context": {"type": "tweet", "id": "1912839915508793621", "text": "@emrahaktasss95 @Facts0601 @DeItaone 😂😂😂😂😂😂😂 logic and facts always break down quick", "author_id": "1329545145583362059", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 39}}, {"conversation_id": "1919942189162938381", "tweet_id": "1920016908671217929", "timestamp": "2025-05-07T07:24:37+00:00", "timestamp_unix": **********, "type": "quote", "text": "<PERSON><PERSON><PERSON> o<PERSON> anketler ve siyaset kurumların önüne geçmiştir .", "context": {"type": "tweet", "id": "1919942189162938381", "text": "China's central bank cuts key rates, injects 1 trillion yuan 3 hours after agreeing to trade talks to prop up economy and give communist party ammo for negotiations.\n\nTomorrow the Fed will do precisely none of that.", "author_id": "********", "author_username": "zerohedge"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 1, "view_count": 103}}, {"conversation_id": "1923002637982519669", "tweet_id": "1923009782862016553", "timestamp": "2025-05-15T13:37:14+00:00", "timestamp_unix": **********, "type": "comment", "text": "@NickTimiraos Law powell go home 😂", "context": {"type": "tweet", "id": "1923333642203373838", "text": "Absolutely leaves no room for a cut.", "author_id": "*********", "author_username": "macroguru9"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 378}}, {"conversation_id": "1923002637982519669", "tweet_id": "1923015657873879099", "timestamp": "2025-05-15T14:00:35+00:00", "timestamp_unix": 1747317635, "type": "comment", "text": "@TKarathanos @NickTimiraos Since I heard that he’s a lawyer, my trust in him when it comes to economics is limitless. 😂", "context": {"type": "tweet", "id": "1923010082956083325", "text": "@NickTimira<PERSON> Nick, how would you rate <PERSON>'s ability to predict the future state of the economy based on his track record? How much weight do you think we should put on his forward-looking statements?", "author_id": "3005225205", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 46}}]}