2025-05-29 13:25:48,535 - interests_batch_processor - INFO - 发现 500 个interests用户数据文件
2025-05-29 13:25:48,535 - interests_batch_processor - INFO - 发现 500 个interests用户数据文件
2025-05-29 13:25:48,536 - interests_batch_processor - INFO - 限制处理前 10 个用户
2025-05-29 13:25:48,536 - interests_batch_processor - INFO - 限制处理前 10 个用户
2025-05-29 13:25:48,537 - interests_batch_processor - INFO - 跳过已处理用户，剩余 10 个用户待处理
2025-05-29 13:25:48,537 - interests_batch_processor - INFO - 跳过已处理用户，剩余 10 个用户待处理
2025-05-29 13:25:48,537 - interests_batch_processor - INFO - 开始批量处理 10 个interests用户
2025-05-29 13:25:48,537 - interests_batch_processor - INFO - 开始批量处理 10 个interests用户
2025-05-29 13:25:48,537 - interests_batch_processor - INFO - 开始处理第 1/2 批，包含 5 个用户
2025-05-29 13:25:48,537 - interests_batch_processor - INFO - 开始处理第 1/2 批，包含 5 个用户
2025-05-29 13:25:48,538 - interests_batch_processor - INFO - 处理用户 1000531264980434944 (1/5) - 总进度: 1/10
2025-05-29 13:25:48,538 - interests_batch_processor - INFO - 处理用户 1000531264980434944 (1/5) - 总进度: 1/10
2025-05-29 13:25:51,063 - interests_processor - INFO - 正在初始化MACIT框架用于interests数据处理...
2025-05-29 13:25:51,125 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-29 13:25:51,125 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-29 13:25:51,126 - data_utils - INFO - 成功加载话题背景信息
2025-05-29 13:25:51,126 - data_utils - INFO - 成功加载话题背景信息
2025-05-29 13:25:51,126 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-29 13:25:51,126 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-29 13:25:51,126 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-29 13:25:51,126 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-29 13:25:51,611 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
2025-05-29 13:25:51,611 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
